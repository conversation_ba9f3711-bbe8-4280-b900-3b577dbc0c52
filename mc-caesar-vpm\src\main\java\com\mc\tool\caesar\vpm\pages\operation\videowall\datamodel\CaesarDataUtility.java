package com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel;

import com.mc.tool.caesar.api.ConfigDataManager;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.datamodel.MatrixDefinitionData;
import com.mc.tool.caesar.api.datamodel.MultiScreenData;
import com.mc.tool.caesar.api.datamodel.MultiScreenData.MultiScreenConInfo;
import com.mc.tool.caesar.api.datamodel.ScreenData;
import com.mc.tool.caesar.api.datamodel.SourceCropData;
import com.mc.tool.caesar.api.datamodel.TxRxGroupData;
import com.mc.tool.caesar.api.datamodel.VideoWallGroupData;
import com.mc.tool.caesar.api.datamodel.VideoWallGroupData.CompleteScreenData;
import com.mc.tool.caesar.api.datamodel.VideoWallGroupData.CompleteVideoData;
import com.mc.tool.caesar.api.datamodel.vp.VpConsoleData;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCpuTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarMatrix;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarTerminalBase;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarVideoWallBgTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.vp.VpGroup;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class CaesarDataUtility {

  /**
   * 复制备份视频墙数据到逻辑视频墙数据.
   *
   * @param vpVideoWallData 备份视频墙数据.
   * @param caesarVideoWallData 逻辑视频墙数据
   * @param configDataManager 数据管理器
   * @param matrix 凯撒矩阵
   */
  public static void vpVideoWallData2CaesarVideoWallData(
      VisualEditModel model,
      VideoWallGroupData.VideoWallData vpVideoWallData,
      CaesarVideoWallData caesarVideoWallData,
      ConfigDataManager configDataManager,
      CaesarMatrix matrix) {
    vpVideoWallData.getLayout(caesarVideoWallData);
    vpVideoWallData.getOsd(caesarVideoWallData.getOsdData());
    vpVideoWallData.getOutput(caesarVideoWallData.getOutputData());
    caesarVideoWallData.setType(vpVideoWallData.getType());

    List<CaesarVideoData> videoList = new ArrayList<>();
    for (int i = 0; i < vpVideoWallData.getVideoCnt(); i++) {
      CompleteVideoData videoData = vpVideoWallData.getCompleteVideoData(i);
      CaesarVideoData logicData = new CaesarVideoData();
      logicData.getXpos().set(videoData.getLeft());
      logicData.getYpos().set(videoData.getTop());
      logicData.getWidth().set(videoData.getWidth());
      logicData.getHeight().set(videoData.getHeight());
      logicData.getAlpha().set((int) (videoData.getAlpha() * 100 + 0.5) / 100.0);
      logicData.getName().set(videoData.getName());
      logicData.getSourceIndex().set(videoData.getSourceIndex());
      logicData.getCropIndex().set(videoData.getCropIndex());
      logicData.getCropName().set(getCropName(configDataManager, videoData.getCropIndex()));
      logicData.getAudioRx().set(videoData.getAudioRx());
      logicData.getAudioSeq().set(videoData.getAudioSeq());
      ExtenderData extenderData = configDataManager.getExtenderDataById(videoData.getCpuId());
      if (extenderData != null) {
        logicData.getSource().set(CaesarDataUtility.findTerminal(model, extenderData));
      }
      videoList.add(logicData);
    }
    caesarVideoWallData.getVideos().setAll(videoList);

    List<CaesarScreenData> screenList = new ArrayList<>();
    for (int i = 0; i < vpVideoWallData.getRows() * vpVideoWallData.getColumns(); i++) {
      CompleteScreenData screenData = vpVideoWallData.getCompleteScreenData(i);
      CaesarScreenData caesarScreenData = new CaesarScreenData();
      screenList.add(caesarScreenData);
      // 查找对应的vpgroup
      for (VpConsoleData vpConsoleData : configDataManager.getActiveEndpointVpconsoles()) {
        if (vpConsoleData.getId() == screenData.getConId()) {
          VpConsoleData parent = vpConsoleData.getParent();
          VpGroup group = matrix.findVpGroup(parent);
          if (parent != null && group != null) {
            int index = parent.getOutportIndex(vpConsoleData);
            if (index >= 0 && index < parent.getOutPortCount()) {
              caesarScreenData.setBingdingScreen(new Pair<>(group, index));
            }
          }
          break;
        }
      }
    }
    caesarVideoWallData.getScreens().setAll(screenList);
    CaesarTestFrameData testFrameData = caesarVideoWallData.getTestFrameData();
    testFrameData.setMode(CaesarTestFrameMode.valueOf(vpVideoWallData.getTestFrameMode()));
    testFrameData.setRgbWithoutAlpha((int) vpVideoWallData.getTestFrameColor());
    testFrameData.setSpeed(vpVideoWallData.getTestFrameSpeed());
    testFrameData.setAlpha(vpVideoWallData.getTestFrameAlpha());

    caesarVideoWallData.getAudioGroupIndex().set(vpVideoWallData.getAudioGroupIndex());
    caesarVideoWallData.getMonitorDecoderGroupIndex().set(vpVideoWallData.getMonitorDecoderGroupIndex());
    caesarVideoWallData.getAutoUnfreezeTimeMs().set(vpVideoWallData.getAutoUnfreezeTimeMs());
  }

  /**
   * 获取设备视频墙数据中用到的所有vpgroup.
   *
   * @param configDataManager config data manager
   * @param matrix matrix
   * @param vpVideoWallData 设备视频墙数据
   * @return 用到的vpgroup的集合
   */
  public static Collection<VpGroup> getVpGroupUsedByVpVideoWallData(
      VisualEditModel model,
      ConfigDataManager configDataManager,
      CaesarMatrix matrix,
      VideoWallGroupData.VideoWallData vpVideoWallData) {
    CaesarVideoWallData tempVideoWallData = new CaesarVideoWallData();
    tempVideoWallData.init();
    CaesarDataUtility.vpVideoWallData2CaesarVideoWallData(
        model, vpVideoWallData, tempVideoWallData, configDataManager, matrix);
    // 记录所有用到的vpcon
    Collection<VpConsoleData> consoleDatas = getUsedVpconFromVideoWall(tempVideoWallData);
    // 如果vpcon不为空，就生成一个视频墙
    List<VpGroup> vpGroups = new ArrayList<>();
    for (VpConsoleData consoleData : consoleDatas) {
      VpGroup group = matrix.findVpGroup(consoleData);
      if (group != null) {
        vpGroups.add(group);
      }
    }

    int count = vpVideoWallData.getVpgroupCount();
    for (int i = 0; i < count; i++) {
      int id = vpVideoWallData.getVpgroups(i);
      VpGroup group = matrix.findVpGroup(id);
      if (group != null && !vpGroups.contains(group)) {
        vpGroups.add(group);
      }
    }

    return vpGroups;
  }

  /** 获取Tx分组使用到的cpu terminal. */
  public static List<CpuData> getCpuDataUsedByCpuGroup(
      ConfigDataManager configDataManager, TxRxGroupData groupData) {
    return configDataManager.getCpuData4TxGroupIndex(groupData.getOid() + 1);
  }

  /**
   * 获取RX分组使用到的con terminal.
   */
  public static List<ConsoleData> getConsoleDataUsedByRxGroup(
      ConfigDataManager configDataManager, TxRxGroupData groupData) {
    return configDataManager.getConsoleDataByTxRxGroupIndex(groupData.getOid() + 1);
  }

  /**
   * 获取视频墙使用到的real vpcon.
   *
   * @param videoWallData 视频墙数据
   * @return 使用到的vpcon的集合。
   */
  public static Collection<VpConsoleData> getUsedVpconFromVideoWall(
      CaesarVideoWallData videoWallData) {
    Set<VpConsoleData> consoleDatas = new HashSet<>();
    for (CaesarScreenData screenData : videoWallData.getScreens()) {
      if (screenData.getVpScreen() == null || screenData.getVpScreen().getParent() == null) {
        continue;
      }
      consoleDatas.add(screenData.getVpScreen().getParent());
    }
    return consoleDatas;
  }

  /**
   * 复制逻辑视频墙数据到备份视频墙数据.
   *
   * @param caesarVideoWallData 逻辑数据
   * @param vpVideoWallData 备份数据
   */
  public static void caesarVideoWallData2VpVideoWallData(
      CaesarVideoWallData caesarVideoWallData, VideoWallGroupData.VideoWallData vpVideoWallData) {

    vpVideoWallData.setLayout(caesarVideoWallData);
    vpVideoWallData.setOsd(caesarVideoWallData.getOsdData());
    vpVideoWallData.setOutput(
        caesarVideoWallData.getOutputData(),
        caesarVideoWallData.getLayoutData().getResWidth().get(),
        caesarVideoWallData.getLayoutData().getResHeight().get());
    vpVideoWallData.setType(caesarVideoWallData.getType());
    vpVideoWallData.setVideoCnt(caesarVideoWallData.getVideos().size());
    for (int i = 0; i < caesarVideoWallData.getVideos().size(); i++) {
      VisualEditTerminal terminal = caesarVideoWallData.getVideos().get(i).getSource().get();
      CpuData cpuData = null;
      ExtenderData extenderData = null;
      if (terminal instanceof CaesarCpuTerminal) {
        cpuData = ((CaesarCpuTerminal) terminal).getCpuData();
      }
      if (cpuData != null) {
        extenderData = cpuData.getExtenderData(0);
      }
      CaesarVideoData logicData = caesarVideoWallData.getVideos().get(i);
      CompleteVideoData videoData = vpVideoWallData.getCompleteVideoData(i);
      videoData.setLeft(logicData.getXpos().get());
      videoData.setTop(logicData.getYpos().get());
      videoData.setWidth(logicData.getWidth().get());
      videoData.setHeight(logicData.getHeight().get());
      videoData.setAlpha(logicData.getAlpha().get());
      videoData.setName(logicData.getName().get());
      videoData.setSourceIndex(logicData.getSourceIndex().get());
      videoData.setCropIndex(logicData.getCropIndex().get());
      videoData.setCpuId(
          extenderData == null ? CompleteVideoData.INVALID_CPU_ID : extenderData.getId());
      videoData.setAudioRx(logicData.getAudioRx().get());
      videoData.setAudioSeq(logicData.getAudioSeq().get());
    }

    for (int i = 0; i < caesarVideoWallData.getScreens().size(); i++) {
      ScreenData screenData = new ScreenData();
      screenData.setOid(i);
      screenData.setConsole(caesarVideoWallData.getScreens().get(i).getVpScreen());
      vpVideoWallData.getCompleteScreenData(i).set(screenData);
    }

    // copy测试画面
    caesarVideoWallData.getTestFrameData().copyTo(vpVideoWallData);

    vpVideoWallData.setAudioGroupIndex(caesarVideoWallData.getAudioGroupIndex().get());
    vpVideoWallData.setMonitorDecoderGroupIndex(caesarVideoWallData.getMonitorDecoderGroupIndex().get());
    vpVideoWallData.setAutoUnfreezeTimeMs(caesarVideoWallData.getAutoUnfreezeTimeMs().get());
  }

  /**
   * 获取设备跨屏数据用到的terminal.
   *
   * @param configDataManager config data manager
   * @param matrix matrix
   * @param data 设备跨屏数据
   * @return 用到的terminal的集合
   */
  public static Collection<VisualEditTerminal> getMultiscreenUsedTerminals(
      ConfigDataManager configDataManager, CaesarMatrix matrix, MultiScreenData data) {
    List<VisualEditTerminal> nodes = new ArrayList<>();
    for (MultiScreenConInfo info : data.getConInfos()) {
      ConsoleData consoleData = configDataManager.getConsoleData4Id(info.getConId());
      if (consoleData == null) {
        continue;
      }
      VisualEditTerminal terminal = matrix.findTerminal(consoleData);
      if (terminal != null) {
        nodes.add(terminal);
      }
    }
    return nodes;
  }

  /**
   * 从数据模型中获取第一个矩阵.
   *
   * @param model 数据模型
   * @return 如果能找到，返回矩阵，否则返回null
   */
  public static CaesarMatrix getFirstMatrix(VisualEditModel model) {
    for (VisualEditNode node : model.getRoots()) {
      if (node instanceof CaesarMatrix) {
        return (CaesarMatrix) node;
      }
    }
    return null;
  }

  /**
   * 查找terminal.
   *
   * @param model 数据模型
   * @param extenderData 外设数据
   * @return 如果找到，返回，否则返回null
   */
  public static CaesarTerminalBase findTerminal(VisualEditModel model, ExtenderData extenderData) {
    for (VisualEditNode node : model.getRoots()) {
      if (node instanceof CaesarMatrix) {
        CaesarMatrix matrix = (CaesarMatrix) node;
        CaesarTerminalBase terminalBase = matrix.findTerminal(extenderData);
        if (terminalBase != null) {
          return terminalBase;
        }
      }
    }
    return null;
  }

  /**
   * 查找terminal.
   *
   * @param model 数据模型
   * @param extenderId 外设id
   * @param isRx 是否为rx
   * @param isTx 是否为tx
   * @return 如果找到返回，否则返回null.
   */
  public static CaesarTerminalBase findTerminal(
      VisualEditModel model, int extenderId, boolean isRx, boolean isTx) {
    for (VisualEditNode node : model.getRoots()) {
      if (node instanceof CaesarMatrix) {
        CaesarMatrix matrix = (CaesarMatrix) node;
        CaesarTerminalBase terminalBase = matrix.findTerminal(extenderId, isRx, isTx);
        if (terminalBase != null) {
          return terminalBase;
        }
      }
    }
    return null;
  }

  /**
   * 查找matrix，如果没有，就新建一个.
   *
   * @return matrix
   */
  public static CaesarMatrix findCaesarMatrix(
      VisualEditModel model, MatrixDefinitionData matrixData) {
    Collection<VisualEditNode> roots = model.getRoots();
    CaesarMatrix matrix = null;
    CaesarMatrix nullIpMatrix = null;
    for (VisualEditNode node : roots) {
      if (node instanceof CaesarMatrix) {
        CaesarMatrix item = (CaesarMatrix) node;
        if (item.getIp().equals(matrixData.getAddress())) {
          matrix = item;
          matrix.setMatrixData(matrixData);
          break;
        } else if (item.getIp().isEmpty()) {
          nullIpMatrix = item;
        }
      }
    }

    if (matrix == null && nullIpMatrix != null) {
      matrix = nullIpMatrix;
      matrix.setMatrixData(matrixData);
    } else if (matrix == null) {
      matrix = new CaesarMatrix();
      matrix.init();
      matrix.setMatrixData(matrixData);
      model.addItem(matrix);
    }
    return matrix;
  }

  /**
   * 查找matrix.
   *
   * @param node 子节点.
   * @return 如果找到，返回，否则返回null.
   */
  public static CaesarMatrix findCaesarMatrix(VisualEditNode node) {
    if (node == null) {
      return null;
    }
    if (node instanceof CaesarMatrix) {
      return (CaesarMatrix) node;
    }
    return findCaesarMatrix(node.getParent());
  }

  /**
   * 添加背景图图层.
   *
   * @param data 视频墙数据.
   */
  public static void addBgImgVideo(CaesarVideoWallData data) {
    CaesarOsdData osdData = data.getOsdData();
    if (!osdData.isEnableBgImg() || osdData.getBgImgWidth() <= 0 || osdData.getBgImgHeight() <= 0) {
      log.info("No background image.");
      return;
    }
    int resWidth = Math.min(osdData.getBgImgWidth(), data.getLayoutData().getTotalWidth());
    int resHeight = Math.min(osdData.getBgImgHeight(), data.getLayoutData().getTotalHeight());
    CaesarVideoData videoData = new CaesarVideoData();
    videoData.getXpos().set(0);
    videoData.getYpos().set(0);
    videoData.getWidth().set(data.getLayoutData().getTotalWidth());
    videoData.getHeight().set(data.getLayoutData().getTotalHeight());
    videoData.getAlpha().set(1);
    videoData.getSource().set(new CaesarVideoWallBgTerminal(resWidth, resHeight));
    data.getVideos().add(0, videoData);
  }

  /**
   * 删除背景图图层.
   *
   * @param data 视频墙数据
   */
  public static void removeBgImgVideo(CaesarVideoWallData data) {
    for (CaesarVideoData videoData : data.getVideos()) {
      if (videoData.getSource().get() instanceof CaesarVideoWallBgTerminal) {
        data.getVideos().remove(videoData);
        break;
      }
    }
  }

  /**
   * 获取裁剪名称.

   * @param cropIndex sourceCropData索引+1
   * @return 裁剪名称
   */
  public static String getCropName(ConfigDataManager dataModel, int cropIndex) {
    if (cropIndex > 0 && cropIndex <= dataModel.getConfigMetaData().getSourceCropDataCount()) {
      SourceCropData cropData = dataModel.getSourceCropData(cropIndex - 1);
      return cropData == null || cropData.getName() == null ? "" : cropData.getName();
    } else {
      return "";
    }
  }
}
