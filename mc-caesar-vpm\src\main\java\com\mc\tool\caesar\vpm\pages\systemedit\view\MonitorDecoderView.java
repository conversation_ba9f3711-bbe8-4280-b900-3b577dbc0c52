package com.mc.tool.caesar.vpm.pages.systemedit.view;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mc.common.beans.SimpleObservable;
import com.mc.common.util.PlatformUtility;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.MonitorDecoderInfo;
import com.mc.tool.caesar.api.datamodel.TxRxGroupDataType;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.api.utils.IpUtil;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCpuTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.TerminalPropertyConverter;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.vp.CpuGroup;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.utility.UndecoratedDialog;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.net.SocketTimeoutException;
import java.net.URL;
import java.util.Collections;
import java.util.concurrent.CompletableFuture;
import javafx.application.Platform;
import javafx.beans.Observable;
import javafx.beans.binding.Bindings;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import javafx.collections.FXCollections;
import javafx.collections.transformation.FilteredListEx;
import javafx.collections.transformation.SortedList;
import javafx.geometry.Pos;
import javafx.scene.control.Button;
import javafx.scene.control.ButtonBar;
import javafx.scene.control.ButtonType;
import javafx.scene.control.ComboBox;
import javafx.scene.control.DialogPaneEx;
import javafx.scene.control.Label;
import javafx.scene.control.TableCell;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableView;
import javafx.scene.control.TextField;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.util.Callback;
import javafx.util.StringConverter;
import lombok.Data;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.controlsfx.validation.ValidationSupport;

/**
 * 监控解码器视图.
 */
@Slf4j
public class MonitorDecoderView extends TableView<CaesarCpuTerminal> {

  /**
   * -- SETTER --
   * .
   */
  @Setter
  private CaesarDeviceController deviceController;

  private final TableColumn<CaesarCpuTerminal, String> indexCol;
  private final TableColumn<CaesarCpuTerminal, Number> idCol;
  private final TableColumn<CaesarCpuTerminal, String> nameCol;
  private final TableColumn<CaesarCpuTerminal, String> portCol;
  private final TableColumn<CaesarCpuTerminal, String> decoderAddrCol;
  private final TableColumn<CaesarCpuTerminal, MonitorDecoderInfo.GridType> defaultGridTypeCol;
  private final TableColumn<CaesarCpuTerminal, String> onlineStatusCol;
  private final TableColumn<CaesarCpuTerminal, String> gatewayAddrCol;
  private FilteredListEx<VisualEditTerminal> filteredListEx;
  private SimpleObservable refreshTrigger = new SimpleObservable();

  private final ObjectMapper objectMapper;

  /**
   * 监控解码器视图.
   */
  public MonitorDecoderView() {
    indexCol = new TableColumn<>(CaesarI18nCommonResource.getString("systemedit.monitor.decoder.index"));
    indexCol.prefWidthProperty().bind(widthProperty().multiply(0.08));
    indexCol.setId("index-col");

    idCol = new TableColumn<>(CaesarI18nCommonResource.getString("systemedit.monitor.decoder.id"));
    idCol.prefWidthProperty().bind(widthProperty().multiply(0.08));
    idCol.setId("id-col");

    nameCol = new TableColumn<>(CaesarI18nCommonResource.getString("systemedit.monitor.decoder.name"));
    nameCol.prefWidthProperty().bind(widthProperty().multiply(0.15));
    nameCol.setId("name-col");

    portCol = new TableColumn<>(CaesarI18nCommonResource.getString("systemedit.monitor.decoder.port"));
    portCol.prefWidthProperty().bind(widthProperty().multiply(0.1));
    portCol.setId("port-col");

    decoderAddrCol = new TableColumn<>(CaesarI18nCommonResource.getString("systemedit.monitor.decoder.address"));
    decoderAddrCol.prefWidthProperty().bind(widthProperty().multiply(0.15));
    decoderAddrCol.setId("decoder-ip-col");

    defaultGridTypeCol = new TableColumn<>("默认网格类型");
    defaultGridTypeCol.prefWidthProperty().bind(widthProperty().multiply(0.12));
    defaultGridTypeCol.setId("default-grid-type-col");

    onlineStatusCol = new TableColumn<>(CaesarI18nCommonResource.getString("systemedit.monitor.decoder.online.status"));
    onlineStatusCol.prefWidthProperty().bind(widthProperty().multiply(0.1));
    onlineStatusCol.setId("online-status-col");

    gatewayAddrCol = new TableColumn<>(CaesarI18nCommonResource.getString("systemedit.monitor.decoder.gateway.address"));
    gatewayAddrCol.prefWidthProperty().bind(widthProperty().multiply(0.12));
    gatewayAddrCol.setId("gateway-ip-col");

    getColumns().addAll(indexCol, idCol, nameCol, portCol, decoderAddrCol,
        defaultGridTypeCol, onlineStatusCol, gatewayAddrCol);

    // 初始化JSON解析器
    objectMapper = new ObjectMapper();

    setupColumns();
  }

  private void setupColumns() {
    indexCol.setCellFactory(new IndexCellFactory());
    indexCol.setStyle("-fx-alignment:CENTER");
    indexCol.setSortable(false);

    idCol.setCellValueFactory(
        (cell) -> {
          CaesarCpuTerminal terminal = cell.getValue();
          return TerminalPropertyConverter.getIdProperty(terminal);
        });
    idCol.setStyle("-fx-alignment:CENTER");

    nameCol.setCellValueFactory(
        (cell) -> TerminalPropertyConverter.getNameProperty(cell.getValue()));

    portCol.setCellValueFactory(
        (cell) -> TerminalPropertyConverter.getPortProperty(cell.getValue()));

    decoderAddrCol.setCellFactory(cell -> new DecoderAddrTableCell(deviceController));

    defaultGridTypeCol.setCellFactory(cell -> new DefaultGridTypeTableCell(deviceController));

    onlineStatusCol.setCellFactory(cell -> new OnlineStatusTableCell());
    gatewayAddrCol.setCellFactory(cell -> new GatewayInfoTableCell());
  }

  /**
   * 刷新列表.
   */
  public void refresh() {
    refreshTrigger.update();
    getColumns().get(5).setVisible(false);
    getColumns().get(5).setVisible(true);
    getColumns().get(6).setVisible(false);
    getColumns().get(6).setVisible(true);
  }

  /**
   * 初始化.
   *
   * @param model model
   */
  public void init(VisualEditModel model) {
    // 初始化列表，过滤条件为监控解码分组中的CaesarCpuTerminal
    filteredListEx =
        new FilteredListEx<>(
            model.getAllTerminals(),
            (item) -> {
              if (item instanceof CaesarCpuTerminal) {
                CaesarCpuTerminal terminal = (CaesarCpuTerminal) item;
                return terminal.getParent() != null
                    && terminal.getParent() instanceof CpuGroup
                    &&
                    ((CpuGroup) terminal.getParent()).getTxRxGroupData() != null
                    &&
                    ((CpuGroup) terminal.getParent()).getTxRxGroupData().getType() == TxRxGroupDataType.TX_MONITOR_DECODER_GROUP;
              }
              return false;
            });

    filteredListEx.setExtractor(
        (item) -> {
          if (item instanceof CaesarCpuTerminal) {
            CaesarCpuTerminal terminal = (CaesarCpuTerminal) item;
            // 监听相关属性变化以触发刷新
            return new Observable[] {
                terminal.nameProperty(),
                terminal.cpuDataProperty(),
                terminal.readOnlyParentProperty(),
                refreshTrigger
            };
          } else {
            return new Observable[] {refreshTrigger};
          }
        });

    SortedList<VisualEditTerminal> sortedList = new SortedList<>(filteredListEx);

    // 使用类型转换来设置items
    @SuppressWarnings("unchecked")
    SortedList<CaesarCpuTerminal> cpuTerminalSortedList = (SortedList<CaesarCpuTerminal>) (Object) sortedList;
    setItems(cpuTerminalSortedList);
  }

  static class IndexCellFactory
      implements Callback<
      TableColumn<CaesarCpuTerminal, String>, TableCell<CaesarCpuTerminal, String>> {

    @Override
    public TableCell<CaesarCpuTerminal, String> call(
        TableColumn<CaesarCpuTerminal, String> param) {
      TableCell<CaesarCpuTerminal, String> cell = new TableCell<>();
      cell.textProperty()
          .bind(
              Bindings.createStringBinding(
                  () -> {
                    if (cell.isEmpty()) {
                      return null;
                    } else {
                      return String.format("%04d", cell.getIndex() + 1);
                    }
                  },
                  cell.indexProperty(),
                  cell.emptyProperty()));
      return cell;
    }
  }

  static class OnlineStatusTableCell extends TableCell<CaesarCpuTerminal, String> {

    private final StringProperty statusProperty =
        new SimpleStringProperty(CaesarI18nCommonResource.getString("systemedit.monitor.decoder.status.detecting"));

    @Override
    public void updateItem(String item, boolean empty) {
      super.updateItem(item, empty);

      if (!empty && getTableRow() != null) {
        Object rowItem = this.getTableRow().getItem();
        if (rowItem instanceof CaesarCpuTerminal) {
          CaesarCpuTerminal terminal = (CaesarCpuTerminal) rowItem;
          textProperty().bind(statusProperty);

          // 异步检测在线状态
          checkOnlineStatus(terminal);
        }
      } else {
        textProperty().unbind();
        setText(null);
        setGraphic(null);
      }
    }

    private void checkOnlineStatus(CaesarCpuTerminal terminal) {
      if (terminal.getCpuData() == null
          || terminal.getCpuData().getScreenOffsetConfig() == null
          || terminal.getCpuData().getScreenOffsetConfig().getMonitorDecoderInfo() == null) {
        Platform.runLater(
            () -> statusProperty.set(CaesarI18nCommonResource.getString("systemedit.monitor.decoder.status.unconfigured")));
        return;
      }

      String ip = IpUtil.getAddressString(
          terminal.getCpuData().getScreenOffsetConfig().getMonitorDecoderInfo().getIp());
      int port = terminal.getCpuData().getScreenOffsetConfig().getMonitorDecoderInfo().getPort();

      if (ip == null || ip.isEmpty() || port <= 0) {
        Platform.runLater(
            () -> statusProperty.set(CaesarI18nCommonResource.getString("systemedit.monitor.decoder.status.unconfigured")));
        return;
      }

      CompletableFuture.supplyAsync(() -> {
        try (Socket socket = new Socket()) {
          socket.connect(new InetSocketAddress(ip, port), 500);
          return CaesarI18nCommonResource.getString("systemedit.monitor.decoder.status.online");
        } catch (SocketTimeoutException e) {
          return CaesarI18nCommonResource.getString("systemedit.monitor.decoder.status.timeout");
        } catch (IOException e) {
          return CaesarI18nCommonResource.getString("systemedit.monitor.decoder.status.offline");
        }
      }).thenAccept(status -> Platform.runLater(() -> statusProperty.set(status)));
    }
  }

  @Data
  static class Address {
    private String ip;
    private int port;
  }

  static class EditDecoderIpDialog extends UndecoratedDialog<Address> {

    public EditDecoderIpDialog(String initialIp, int port) {
      setTitle(CaesarI18nCommonResource.getString("systemedit.monitor.decoder.dialog.title"));
      VBox content = new VBox();
      Label ipLabel = new Label(CaesarI18nCommonResource.getString("systemedit.monitor.decoder.dialog.ip.label"));
      TextField ipField = new TextField(initialIp);
      HBox hbox = new HBox(ipLabel, ipField);
      hbox.setSpacing(10);
      hbox.setAlignment(Pos.CENTER_LEFT);
      content.getChildren().add(hbox);
      Label portLabel = new Label(CaesarI18nCommonResource.getString("systemedit.monitor.decoder.dialog.port.label"));
      TextField portField = new TextField(String.valueOf(port));
      HBox portHbox = new HBox(portLabel, portField);
      portHbox.setSpacing(10);
      portHbox.setAlignment(Pos.CENTER_LEFT);
      content.getChildren().add(portHbox);
      getDialogPane().setContent(content);

      final DialogPaneEx dialogPane = getDialogPane();
      dialogPane.getButtonTypes().addAll(ButtonType.OK, ButtonType.CANCEL);
      ValidationSupport validationSupport = new ValidationSupport();
      validationSupport.setValidationDecorator(null);
      validationSupport.registerValidator(ipField, new IpValidator());
      dialogPane
          .lookupButton(ButtonType.OK)
          .disableProperty()
          .bind(validationSupport.invalidProperty());
      setResultConverter(
          (dialogButton) -> {
            ButtonBar.ButtonData data = dialogButton == null ? null : dialogButton.getButtonData();
            if (data == ButtonBar.ButtonData.OK_DONE) {
              Address address = new Address();
              address.setIp(ipField.getText().trim());
              try {
                address.setPort(Integer.parseInt(portField.getText().trim()));
              } catch (NumberFormatException e) {
                address.setPort(0);
              }
              return address;
            }
            return null;
          });
    }

  }


  static class DecoderAddrTableCell extends TableCell<CaesarCpuTerminal, String> {

    private final CaesarDeviceController deviceController;

    public DecoderAddrTableCell(CaesarDeviceController deviceController) {
      this.deviceController = deviceController;
    }

    @Override
    public void updateItem(String item, boolean empty) {
      super.updateItem(item, empty);
      this.setText(null);
      this.setGraphic(null);
      if (!empty && getTableRow() != null) {
        Object rowItem = this.getTableRow().getItem();
        if (rowItem instanceof CaesarCpuTerminal) {
          CpuData data = ((CaesarCpuTerminal) rowItem).getCpuData();
          Label ipLabel = new Label(String.format("%s:%d", data.getMonitorDecoderIpString(),
              data.getMonitorDecoderPort()));
          deviceController
              .getDataModel()
              .addPropertyChangeListener(
                  new String[] {
                      CpuData.PROPERTY_DECODER_IP,
                      CpuData.PROPERTY_DECODER_PORT
                  },
                  pce -> {
                    PlatformUtility.runInFxThread(
                        () -> ipLabel.setText(String.format("%s:%d", data.getMonitorDecoderIpString(),
                            data.getMonitorDecoderPort())));
                  });
          ImageView iv =
              new ImageView(new Image("/com/mc/tool/caesar/vpm/pages/systemedit/edit.png"));
          Button button = new Button();
          button.getStyleClass().add("image-button");
          button.setGraphic(iv);
          HBox hbox = new HBox(ipLabel, button);
          hbox.setAlignment(Pos.CENTER_LEFT);
          this.setGraphic(hbox);
          button.setOnAction(
              event -> {
                EditDecoderIpDialog dialog =
                    new EditDecoderIpDialog(data.getMonitorDecoderIpString(), data.getMonitorDecoderPort());
                dialog.initOwner(button.getScene().getWindow());
                dialog.showAndWait();
                Address addr = dialog.getResult();
                if (addr != null
                    && (!addr.ip.equals(data.getMonitorDecoderIpString()) || addr.port != data.getMonitorDecoderPort())) {
                  deviceController.execute(
                      () -> {
                        byte[] bytes = IpUtil.getAddressByte(addr.ip);
                        data.setMonitorDecoderIp(bytes);
                        data.setMonitorDecoderPort(addr.port);
                        try {
                          deviceController
                              .getDataModel()
                              .sendCpuData(Collections.singletonList(data));
                        } catch (DeviceConnectionException | BusyException exception) {
                          log.error("Fail to send cpuData value!", exception);
                        }
                      });

                }
              });
        }
      } else {
        this.setText(null);
        this.setGraphic(null);
      }
    }
  }

  class GatewayInfoTableCell extends TableCell<CaesarCpuTerminal, String> {

    private final StringProperty infoProperty =
        new SimpleStringProperty(CaesarI18nCommonResource.getString("systemedit.monitor.decoder.status.fetching"));

    @Override
    public void updateItem(String item, boolean empty) {
      super.updateItem(item, empty);

      if (!empty && getTableRow() != null) {
        Object rowItem = this.getTableRow().getItem();
        if (rowItem instanceof CaesarCpuTerminal) {
          CaesarCpuTerminal terminal = (CaesarCpuTerminal) rowItem;
          textProperty().bind(infoProperty);

          // 异步获取网关信息
          fetchGatewayInfo(terminal);
        }
      } else {
        textProperty().unbind();
        setText(null);
        setGraphic(null);
      }
    }

    private void fetchGatewayInfo(CaesarCpuTerminal terminal) {
      if (terminal.getCpuData() == null
          || terminal.getCpuData().getScreenOffsetConfig() == null
          || terminal.getCpuData().getScreenOffsetConfig().getMonitorDecoderInfo() == null) {
        Platform.runLater(
            () -> infoProperty.set(CaesarI18nCommonResource.getString("systemedit.monitor.decoder.status.unconfigured")));
        return;
      }

      String ip = IpUtil.getAddressString(
          terminal.getCpuData().getScreenOffsetConfig().getMonitorDecoderInfo().getIp());
      int port = terminal.getCpuData().getScreenOffsetConfig().getMonitorDecoderInfo().getPort();

      if (ip == null || ip.isEmpty() || port <= 0) {
        Platform.runLater(
            () -> infoProperty.set(CaesarI18nCommonResource.getString("systemedit.monitor.decoder.status.unconfigured")));
        return;
      }

      String url = String.format("http://%s:%d/api/v1/gateway", ip, port);

      CompletableFuture.supplyAsync(() -> {
        try {
          URL urlObj = new URL(url);
          HttpURLConnection connection = (HttpURLConnection) urlObj.openConnection();
          connection.setConnectTimeout(500);
          connection.setReadTimeout(500);
          connection.setRequestMethod("GET");

          int responseCode = connection.getResponseCode();
          if (responseCode == HttpURLConnection.HTTP_OK) {
            try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(connection.getInputStream(), "UTF-8"))) {
              StringBuilder responseBody = new StringBuilder();
              String line;
              while ((line = reader.readLine()) != null) {
                responseBody.append(line);
              }

              JsonNode jsonNode = objectMapper.readTree(responseBody.toString());

              JsonNode ipNode = jsonNode.get("ip");
              JsonNode portNode = jsonNode.get("port");
              return ipNode != null && portNode != null ? ipNode.asText() + ":" + portNode.asText() :
                  CaesarI18nCommonResource.getString("systemedit.monitor.decoder.status.unknown");
            }
          } else {
            return CaesarI18nCommonResource.getString("systemedit.monitor.decoder.status.fetch.failed");
          }
        } catch (Exception e) {
          log.debug("Failed to fetch gateway info from {}: {}", url, e.getMessage());
          return CaesarI18nCommonResource.getString("systemedit.monitor.decoder.status.fetch.failed");
        }
      }).thenAccept(info -> Platform.runLater(() -> infoProperty.set(info)));
    }
  }

  /**
   * 默认网格类型表格单元格.
   */
  static class DefaultGridTypeTableCell extends TableCell<CaesarCpuTerminal, MonitorDecoderInfo.GridType> {

    private final CaesarDeviceController deviceController;
    private ComboBox<MonitorDecoderInfo.GridType> comboBox;

    public DefaultGridTypeTableCell(CaesarDeviceController deviceController) {
      this.deviceController = deviceController;
    }

    @Override
    public void updateItem(MonitorDecoderInfo.GridType item, boolean empty) {
      super.updateItem(item, empty);

      if (empty || getTableRow() == null) {
        setText(null);
        setGraphic(null);
        return;
      }

      Object rowItem = getTableRow().getItem();
      if (!(rowItem instanceof CaesarCpuTerminal)) {
        setText(null);
        setGraphic(null);
        return;
      }

      CaesarCpuTerminal terminal = (CaesarCpuTerminal) rowItem;
      CpuData data = terminal.getCpuData();
      if (data == null) {
        setText(null);
        setGraphic(null);
        return;
      }

      if (comboBox == null) {
        comboBox = new ComboBox<>(FXCollections.observableArrayList(MonitorDecoderInfo.GridType.values()));
        comboBox.setConverter(new GridTypeStringConverter());
        comboBox.setMaxWidth(Double.MAX_VALUE);

        comboBox.valueProperty().addListener((obs, oldVal, newVal) -> {
          if (newVal != null && !newVal.equals(data.getMonitorDecoderDefaultGridType())) {
            deviceController.execute(() -> {
              data.setMonitorDecoderDefaultGridType(newVal);
            });
          }
        });

        // 监听属性变化来更新ComboBox的值
        deviceController.getDataModel().addPropertyChangeListener(
            CpuData.PROPERTY_DECODER_DEFAULT_GRID_TYPE,
            pce -> {
              if (pce.getSource() == data) {
                PlatformUtility.runInFxThread(() -> {
                  MonitorDecoderInfo.GridType newGridType = data.getMonitorDecoderDefaultGridType();
                  if (!newGridType.equals(comboBox.getValue())) {
                    comboBox.setValue(newGridType);
                  }
                });
              }
            });
      }

      // 设置当前值
      MonitorDecoderInfo.GridType currentGridType = data.getMonitorDecoderDefaultGridType();
      if (!currentGridType.equals(comboBox.getValue())) {
        comboBox.setValue(currentGridType);
      }

      setGraphic(comboBox);
      setText(null);
    }
  }

  /**
   * GridType 字符串转换器.
   */
  static class GridTypeStringConverter extends StringConverter<MonitorDecoderInfo.GridType> {
    @Override
    public String toString(MonitorDecoderInfo.GridType gridType) {
      if (gridType == null) {
        return "";
      }
      switch (gridType) {
        case NONE:
          return "无";
        case GRID_4:
          return "4分屏";
        case GRID_9:
          return "9分屏";
        case GRID_16:
          return "16分屏";
        case GRID_25:
          return "25分屏";
        default:
          return gridType.name();
      }
    }

    @Override
    public MonitorDecoderInfo.GridType fromString(String string) {
      if (string == null || string.trim().isEmpty()) {
        return MonitorDecoderInfo.GridType.NONE;
      }
      switch (string) {
        case "全屏":
          return MonitorDecoderInfo.GridType.NONE;
        case "4分屏":
          return MonitorDecoderInfo.GridType.GRID_4;
        case "9分屏":
          return MonitorDecoderInfo.GridType.GRID_9;
        case "16分屏":
          return MonitorDecoderInfo.GridType.GRID_16;
        case "25分屏":
          return MonitorDecoderInfo.GridType.GRID_25;
        default:
          return MonitorDecoderInfo.GridType.NONE;
      }
    }
  }
}
